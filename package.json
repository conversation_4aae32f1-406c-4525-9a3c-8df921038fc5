{"name": "namecheap-proxy", "version": "1.0.0", "description": "Node.js Express TypeScript proxy server for Namecheap API", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean"}, "keywords": ["namecheap", "proxy", "express", "typescript", "api"], "author": "blackingdev", "license": "MIT", "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "xml2js": "^0.6.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.8.0", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/xml2js": "^0.4.14", "typescript": "^5.2.2", "ts-node-dev": "^2.0.0", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0"}}