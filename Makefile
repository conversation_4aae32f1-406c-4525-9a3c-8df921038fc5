.PHONY: help build up down logs clean dev prod install test

# Default target
help:
	@echo "Available commands:"
	@echo "  make install    - Install dependencies"
	@echo "  make dev        - Run in development mode with Docker"
	@echo "  make prod       - Run in production mode with Docker"
	@echo "  make build      - Build Docker images"
	@echo "  make up         - Start services"
	@echo "  make down       - Stop services"
	@echo "  make logs       - View logs"
	@echo "  make clean      - Clean up containers and images"
	@echo "  make test       - Run tests"

# Install dependencies locally
install:
	npm install

# Development mode
dev:
	docker-compose -f docker-compose.dev.yml up --build

# Production mode
prod:
	docker-compose up --build

# Build images
build:
	docker-compose build

# Start services
up:
	docker-compose up -d

# Stop services
down:
	docker-compose down

# View logs
logs:
	docker-compose logs -f

# Clean up
clean:
	docker-compose down -v --rmi all --remove-orphans
	docker system prune -f

# Run tests (placeholder)
test:
	npm test
