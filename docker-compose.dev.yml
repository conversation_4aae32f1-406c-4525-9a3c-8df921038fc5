version: '3.8'

services:
  api:
    build:
      context: .
      target: development
    container_name: namecheap-proxy-api-dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - PORT=3000
      - NAMECHEAP_API_USER=${NAMECHEAP_API_USER:-blackingdev}
      - NAMECHEAP_API_KEY=${NAMECHEAP_API_KEY:-84523b0158f340c9be472d6a8d50c309}
      - NAMECHEAP_USERNAME=${NAMECHEAP_USERNAME:-blackingdev}
      - NAMECHEAP_CLIENT_IP=${NAMECHEAP_CLIENT_IP:-*************}
      - NAMECHEAP_BASE_URL=${NAMECHEAP_BASE_URL:-https://api.sandbox.namecheap.com/xml.response}
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: namecheap-proxy-nginx-dev
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    # No longer depends on local API service since we're proxying to external backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
