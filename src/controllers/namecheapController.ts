import { Request, Response, NextFunction } from 'express';
import axios from 'axios';
import { parseString } from 'xml2js';
import { promisify } from 'util';
import { namecheapConfig, getNamecheapBaseParams } from '../config/namecheap';
import { Logger } from '../utils/logger';

const parseXML = promisify(parseString);

export class NamecheapController {
  /**
   * Simple proxy that forwards all requests to Namecheap API
   */
  static async proxyRequest(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      // Merge base parameters with request parameters
      const allParams = {
        ...getNamecheapBaseParams(),
        ...req.query,
        ...req.body
      };

      Logger.info('Proxying request to Namecheap API', {
        method: req.method,
        params: Object.keys(allParams)
      });

      const response = await axios.get(namecheapConfig.baseUrl, {
        params: allParams,
        timeout: 30000,
        headers: {
          'User-Agent': 'NamecheapProxy/1.0.0'
        }
      });

      Logger.info('Received response from Namecheap API', {
        status: response.status,
        contentType: response.headers['content-type']
      });

      // Parse XML to JSON
      const parsedData = await parseXML(response.data);

      // Set content type to JSON
      res.set('Content-Type', 'application/json');

      // Return JSON response
      res.status(response.status).json({
        success: true,
        data: parsedData,
        originalXml: response.data
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        Logger.error('Namecheap API request failed', {
          message: error.message,
          status: error.response?.status,
          data: error.response?.data
        });
        // Try to parse error response as JSON if possible
        if (error.response) {
          try {
            const errorData = await parseXML(error.response.data);
            res.status(error.response.status).json({
              success: false,
              error: errorData,
              originalXml: error.response.data
            });
          } catch (parseError) {
            res.status(error.response.status).json({
              success: false,
              error: 'Failed to parse error response',
              originalXml: error.response.data
            });
          }
        } else {
          res.status(500).json({
            success: false,
            error: 'Failed to connect to Namecheap API',
            message: error.message
          });
        }
      } else {
        next(error);
      }
    }
  }
}
