import { NamecheapConfig } from '../types';

export const namecheapConfig: NamecheapConfig = {
  apiUser: process.env.NAMECHEAP_API_USER || '',
  apiKey: process.env.NAMECHEAP_API_KEY || '',
  userName: process.env.NAMECHEAP_USERNAME || '',
  clientIp: process.env.NAMECHEAP_CLIENT_IP || '',
  baseUrl: process.env.NAMECHEAP_BASE_URL || 'https://api.sandbox.namecheap.com/xml.response'
};

export const getNamecheapBaseParams = () => ({
  ApiUser: namecheapConfig.apiUser,
  ApiKey: namecheapConfig.apiKey,
  UserName: namecheapConfig.userName,
  ClientIp: namecheapConfig.clientIp
});
