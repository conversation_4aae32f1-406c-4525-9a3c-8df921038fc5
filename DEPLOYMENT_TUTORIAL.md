# Ubuntu 24.04 Deployment Tutorial for Namecheap Proxy

This tutorial will guide you through deploying the Namecheap Proxy project on Ubuntu 24.04 using GitHub to clone the project.

## Prerequisites

- Ubuntu 24.04 server with sudo access
- Internet connection
- Basic knowledge of Linux command line

## Step 1: Update System

First, update your Ubuntu system to ensure all packages are current:

```bash
sudo apt update && sudo apt upgrade -y
```

## Step 2: Install Required Dependencies

### Install Git
```bash
sudo apt install git -y
```

### Install Node.js 18+ (using NodeSource repository)
```bash
# Add NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# Install Node.js
sudo apt install nodejs -y

# Verify installation
node --version
npm --version
```

### Install Docker and Docker Compose
```bash
# Install Docker
sudo apt install apt-transport-https ca-certificates curl software-properties-common -y
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io -y

# Install Docker Compose
sudo apt install docker-compose-plugin -y

# Add current user to docker group
sudo usermod -aG docker $USER

# Apply group changes (logout and login again, or use newgrp)
newgrp docker

# Verify Docker installation
docker --version
docker compose version
```

### Install Nginx (Optional - for reverse proxy without Docker)
```bash
sudo apt install nginx -y
```

## Step 3: Clone the Private Project from GitHub

Since this is a private repository, you'll need to authenticate with GitHub. Choose one of the following methods:

### Method A: Using SSH Keys (Recommended)

First, generate an SSH key if you don't have one:

```bash
# Generate SSH key (press Enter for default location and empty passphrase for automation)
ssh-keygen -t ed25519 -C "<EMAIL>"

# Start SSH agent
eval "$(ssh-agent -s)"

# Add SSH key to agent
ssh-add ~/.ssh/id_ed25519

# Display public key to copy to GitHub
cat ~/.ssh/id_ed25519.pub
```

Add the SSH key to your GitHub account:
1. Copy the output from the `cat` command above
2. Go to GitHub → Settings → SSH and GPG keys → New SSH key
3. Paste the key and save

Test SSH connection:
```bash
ssh -T **************
```

Clone the repository:
```bash
# Navigate to your preferred directory
cd /opt

# Clone the private repository using SSH
sudo <NAME_EMAIL>:your-username/namecheap-proxy.git

# Change ownership to current user
sudo chown -R $USER:$USER namecheap-proxy

# Navigate to project directory
cd namecheap-proxy
```

### Method B: Using Personal Access Token (HTTPS)

Create a Personal Access Token:
1. Go to GitHub → Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Generate new token with `repo` scope
3. Copy the token (you won't see it again)

Clone using HTTPS with token:
```bash
# Navigate to your preferred directory
cd /opt

# Clone using HTTPS with token (replace TOKEN and username)
sudo git clone https://<EMAIL>/your-username/namecheap-proxy.git

# Or clone and enter credentials when prompted
sudo git clone https://github.com/your-username/namecheap-proxy.git

# Change ownership to current user
sudo chown -R $USER:$USER namecheap-proxy

# Navigate to project directory
cd namecheap-proxy
```

### Method C: Using GitHub CLI (Alternative)

Install GitHub CLI:
```bash
# Install GitHub CLI
sudo apt install gh -y

# Authenticate with GitHub
gh auth login

# Clone the repository
cd /opt
sudo gh repo clone your-username/namecheap-proxy

# Change ownership to current user
sudo chown -R $USER:$USER namecheap-proxy

# Navigate to project directory
cd namecheap-proxy
```

## Step 4: Configure Environment Variables

```bash
# Copy the example environment file
cp .env.example .env

# Edit the environment file with your settings
nano .env
```

Example `.env` configuration:
```env
NODE_ENV=production
PORT=3000
NAMECHEAP_API_USER=your_api_user
NAMECHEAP_API_KEY=your_api_key
NAMECHEAP_USERNAME=your_username
NAMECHEAP_CLIENT_IP=your_server_ip
NAMECHEAP_BASE_URL=https://api.namecheap.com/xml.response
```

## Step 5: Deployment Options

### Option A: Docker Deployment (Recommended)

This is the easiest and most reliable method:

```bash
# Build and start the application with Docker Compose
docker compose up -d --build

# Check if containers are running
docker compose ps

# View logs
docker compose logs -f
```

The application will be available at:
- Production: `http://your-server-ip` (port 80)
- API directly: `http://your-server-ip:3000`

### Option B: Direct Node.js Deployment

If you prefer to run without Docker:

```bash
# Install project dependencies
npm install

# Build the TypeScript project
npm run build

# Install PM2 for process management
sudo npm install -g pm2

# Start the application with PM2
pm2 start dist/index.js --name "namecheap-proxy"

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
# Follow the instructions provided by the command above
```

## Step 6: Configure Firewall

```bash
# Enable UFW firewall
sudo ufw enable

# Allow SSH (important!)
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# If running Node.js directly, allow port 3000
sudo ufw allow 3000

# Check firewall status
sudo ufw status
```

## Step 7: Set Up Nginx Reverse Proxy (Optional for Node.js deployment)

If you're running Node.js directly and want to use Nginx as a reverse proxy:

```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/namecheap-proxy
```

Add the following configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com;  # Replace with your domain or server IP

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
# Create symbolic link
sudo ln -s /etc/nginx/sites-available/namecheap-proxy /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx

# Enable Nginx to start on boot
sudo systemctl enable nginx
```

## Step 8: Verify Deployment

Test your deployment:

```bash
# Test health endpoint
curl http://localhost/health

# Or if running on port 3000
curl http://localhost:3000/health

# Test the proxy endpoint
curl "http://localhost/api/proxy?Command=namecheap.domains.check&DomainList=example.com"
```

## Step 9: Set Up SSL Certificate (Optional but Recommended)

Install Certbot for Let's Encrypt SSL:

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate (replace with your domain)
sudo certbot --nginx -d your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

## Step 10: Monitoring and Maintenance

### View Application Logs

For Docker deployment:
```bash
# View all logs
docker compose logs -f

# View specific service logs
docker compose logs -f api
docker compose logs -f nginx
```

For PM2 deployment:
```bash
# View PM2 logs
pm2 logs namecheap-proxy

# Monitor PM2 processes
pm2 monit
```

### Update the Application

```bash
# Navigate to project directory
cd /opt/namecheap-proxy

# Pull latest changes from GitHub (works with SSH or configured HTTPS)
git pull origin main

# If using personal access token and getting authentication errors:
# git remote set-url origin https://<EMAIL>/your-username/namecheap-proxy.git
# git pull origin main

# For Docker deployment
docker compose down
docker compose up -d --build

# For PM2 deployment
npm run build
pm2 restart namecheap-proxy
```

### System Monitoring

```bash
# Check system resources
htop

# Check disk usage
df -h

# Check memory usage
free -h

# Check Docker container status
docker compose ps
```

## Troubleshooting

### Common Issues

1. **Port already in use**: Check if another service is using the port
   ```bash
   sudo netstat -tlnp | grep :80
   sudo netstat -tlnp | grep :3000
   ```

2. **Permission denied**: Ensure proper file permissions
   ```bash
   sudo chown -R $USER:$USER /opt/namecheap-proxy
   ```

3. **Docker permission denied**: Add user to docker group
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

4. **Git authentication failed**:
   - For SSH: Check if SSH key is added to GitHub and SSH agent
   ```bash
   ssh -T **************
   ssh-add -l
   ```
   - For HTTPS: Verify personal access token is correct and has repo permissions
   ```bash
   git remote -v
   git remote set-url origin https://<EMAIL>/your-username/namecheap-proxy.git
   ```

5. **Application not starting**: Check logs for errors
   ```bash
   docker compose logs
   # or
   pm2 logs namecheap-proxy
   ```

## Security Considerations

1. **Keep system updated**: Regularly update Ubuntu and packages
2. **Use strong passwords**: For server access and API keys
3. **Configure firewall**: Only allow necessary ports
4. **Use SSL certificates**: Encrypt traffic with HTTPS
5. **Regular backups**: Backup your configuration and data
6. **Monitor logs**: Check for suspicious activity

## Conclusion

Your Namecheap Proxy application should now be successfully deployed on Ubuntu 24.04. The application provides a secure proxy interface to the Namecheap API with proper error handling, logging, and monitoring capabilities.

For production use, consider:
- Setting up automated backups
- Implementing log rotation
- Setting up monitoring alerts
- Using a domain name with SSL certificate
- Implementing additional security measures
