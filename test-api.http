### Health Check
GET http://localhost/health

### Health Check (Development)
GET http://localhost:8080/health

### Proxy Request - Domain Check (GET with query params) - Returns JSON
GET http://localhost/api/proxy?Command=namecheap.domains.check&DomainList=example.com,test.org

### Proxy Request - Domain Check (POST with body) - Returns JSON
POST http://localhost/api/proxy
Content-Type: application/json

{
  "Command": "namecheap.domains.check",
  "DomainList": "example.com,test.org"
}

### Proxy Request - Development (GET) - Returns JSON
GET http://localhost:8080/api/proxy?Command=namecheap.domains.check&DomainList=google.com,facebook.com

### Proxy Request - Development (POST) - Returns JSON
POST http://localhost:8080/api/proxy
Content-Type: application/json

{
  "Command": "namecheap.domains.check",
  "DomainList": "google.com,facebook.com"
}
